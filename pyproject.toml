[project]
name = "cmsdata-china"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.12"
dependencies = [
    "aiomysql>=0.2.0",
    "asyncpg>=0.30.0",
    "bcrypt>=4.3.0",
    "cryptography>=45.0.6",
    "et-xmlfile>=2.0.0",
    "firebirdsql>=1.4.0",
    "invoke>=2.2.0",
    "numpy>=2.3.2",
    "openpyxl>=3.1.5",
    "oss2>=2.19.1",
    "pandas>=2.3.1",
    "paramiko>=4.0.0",
    "passlib>=1.7.4",
    "psycopg2-binary>=2.9.10",
    "pymysql>=1.1.1",
    "python-dateutil>=2.9.0",
    "python-dotenv>=1.1.1",
    "pytz>=2025.2",
    "sshtunnel>=0.4.0",
]
