# Pro2数据库基本操作

import os
import asyncio
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv
from utils.database.db_pro2_sea_air_profit import (
    get_booking_details_with_transhipment,
    query_job_details_with_statistics_by_date
)
from utils.basic.data_cache_manager import async_cached_data_function
from utils.basic.logger_config import setup_logger

load_dotenv(override=True)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

# 批处理配置 - 支持环境变量配置
BATCH_SIZE_DAYS = int(os.getenv('BATCH_SIZE_DAYS', 3))  # 每个批次处理的天数
MAX_CONCURRENT_BATCHES = int(os.getenv('MAX_CONCURRENT_BATCHES', 5))  # 最大并发批次数

def _validate_date_range(begin_date: str, end_date: str) -> tuple[datetime, datetime]:
    """
    验证并转换日期范围
    """
    try:
        start_date = datetime.strptime(begin_date, '%Y-%m-%d')
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
        
        if start_date > end_date_obj:
            raise ValueError(f"开始日期 {begin_date} 不能晚于结束日期 {end_date}")
        
        # 检查日期范围是否过长（超过1年）
        if (end_date_obj - start_date).days > 365:
            logger.warning(f"日期范围过长({(end_date_obj - start_date).days}天)，建议分段查询")
        
        return start_date, end_date_obj
    except ValueError as e:
        logger.error(f"日期格式验证失败: {e}")
        raise


def _split_date_range(begin_date: str, end_date: str) -> List[tuple]:
    """
    将时间范围拆分为小批次
    """
    try:
        start_date, end_date_obj = _validate_date_range(begin_date, end_date)
        
        batches = []
        current_date = start_date
        
        while current_date <= end_date_obj:
            batch_end = min(current_date + timedelta(days=BATCH_SIZE_DAYS - 1), end_date_obj)
            batches.append((
                current_date.strftime('%Y-%m-%d'),
                batch_end.strftime('%Y-%m-%d')
            ))
            current_date = batch_end + timedelta(days=1)
        
        return batches
    except Exception as e:
        logger.error(f"拆分日期范围失败: {e}")
        return [(begin_date, end_date)]

async def _process_batch_with_semaphore(
    semaphore: asyncio.Semaphore, 
    batch_func: Callable, 
    batch_start: str, 
    batch_end: str, 
    logger_prefix: str
) -> Any:
    """
    使用信号量控制并发的批处理
    """
    async with semaphore:
        try:
            return await asyncio.to_thread(
                batch_func,
                batch_start,
                batch_end,
                logger_prefix=logger_prefix
            )
        except Exception as e:
            logger.error(f"批次处理失败 {batch_start}-{batch_end}: {e}")
            raise


async def _batch_process_data(
    begin_date: str,
    end_date: str,
    batch_func: Callable,
    data_type: str,
    allow_empty_results: bool = False
) -> Dict[str, Any]:
    """
    通用的批处理数据查询函数
    """
    logger.warning(f"开始查询{data_type}: {begin_date} 到 {end_date}")
    
    try:
        # 验证日期范围
        start_date, end_date_obj = _validate_date_range(begin_date, end_date)
        date_diff = (end_date_obj - start_date).days
        
        # 如果时间范围小于等于批次大小，直接查询
        if date_diff <= BATCH_SIZE_DAYS:
            logger.warning(f"时间范围较小({date_diff}天)，直接查询")
            results = await asyncio.to_thread(
                batch_func,
                begin_date,
                end_date,
                logger_prefix="[Scheduler]"
            )
            
            # 处理空结果
            if not results and not allow_empty_results:
                logger.warning(f"未查询到{data_type}: {begin_date} 到 {end_date}")
                return {
                    'data': [],
                    'total_count': 0,
                    'query_info': {
                        'date_range': f'{begin_date} 到 {end_date}',
                        'data_type': data_type,
                        'message': '未查询到数据'
                    }
                }
            
            logger.warning(f"{data_type}查询完成: {len(results or [])} 条记录")
            
            return {
                'data': results or [],
                'total_count': len(results or []),
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': data_type
                }
            }
        
        # 大时间范围使用分批处理
        logger.warning(f"时间范围较大({date_diff}天)，使用分批处理策略")
        batches = _split_date_range(begin_date, end_date)
        logger.warning(f"拆分为 {len(batches)} 个批次，每批次最多{BATCH_SIZE_DAYS}天")
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_BATCHES)
        
        # 创建批处理任务
        tasks = []
        for batch_start, batch_end in batches:
            task = _process_batch_with_semaphore(
                semaphore,
                batch_func,
                batch_start,
                batch_end,
                f"[Scheduler-Batch-{batch_start}]"
            )
            tasks.append(task)
        
        # 并发执行所有批次
        logger.warning(f"开始并发执行 {len(tasks)} 个批次任务")
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并结果
        all_results = []
        failed_batches = []
        
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                failed_batches.append(batches[i])
                logger.error(f"批次 {batches[i]} 查询失败: {result}")
            elif result:
                all_results.extend(result)
                logger.info(f"批次 {batches[i]} 完成，获得 {len(result)} 条记录")
            elif allow_empty_results:
                logger.info(f"批次 {batches[i]} 完成，无数据返回（允许空结果）")
        
        logger.warning(f"所有批次处理完成，共获得 {len(all_results)} 条记录")
        if failed_batches:
            logger.warning(f"失败批次: {failed_batches}")
        
        return {
            'data': all_results,
            'total_count': len(all_results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': data_type,
                'batch_count': len(batches),
                'failed_batches': failed_batches
            }
        }
        
    except Exception as e:
        logger.error(f"查询{data_type}失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': data_type,
                'error': f'查询失败: {str(e)}'
            }
        }


# 根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_sea_air_profit_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
    使用分批处理策略优化大时间范围查询性能
    """
    return await _batch_process_data(
        begin_date=begin_date,
        end_date=end_date,
        batch_func=get_booking_details_with_transhipment,
        data_type="全部订舱毛利数据（含转运）",
        allow_empty_results=True  # 海运空运数据允许空结果
    )

# 根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_job_details_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
    使用分批处理策略优化大时间范围查询性能
    """
    return await _batch_process_data(
        begin_date=begin_date,
        end_date=end_date,
        batch_func=query_job_details_with_statistics_by_date,
        data_type="全部作业明细数据（含转运）",
        allow_empty_results=False  # 作业明细数据不允许空结果
    )
