# Firebird BLOB字段处理工具模块

import re
from utils.basic.fb_conn import execute_pro2_query_async
from utils.basic.logger_config import setup_logger

logger = setup_logger(__name__, level="warning")

async def update_blob_text_field_safe(table_name, field_name, id_field, record_id, text_content, charset='UTF-8'):
    """
    通用的 BLOB SUB_TYPE TEXT 字段安全更新函数
    
    解决 firebirdsql Python 驱动程序不支持参数化查询向 BLOB 字段写入的问题
    
    Args:
        table_name: 表名
        field_name: BLOB 字段名
        id_field: 主键字段名
        record_id: 记录ID
        text_content: 要写入的文本内容（None表示设置为NULL）
        charset: 字符编码，默认UTF-8
        
    Returns:
        bool: 是否成功更新
        
    Security:
        - 防SQL注入：转义单引号
        - 参数验证：验证表名、字段名格式
        - 字符串验证：确保ID为字符串格式
        
    Usage:
        # 更新公司地址
        await update_blob_text_field_safe('company', 'address', 'code', 'C1234', '公司地址')
        
        # 更新公司备注
        await update_blob_text_field_safe('company', 'remarks', 'code', 'C1234', '备注内容')
        
        # 设置为NULL
        await update_blob_text_field_safe('company', 'address', 'code', 'C1234', None)
    """
    try:
        # 参数验证
        if not table_name or not field_name or not id_field:
            raise ValueError("表名、字段名和ID字段不能为空")
        
        # 验证表名和字段名格式（防止SQL注入）
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', table_name):
            raise ValueError(f"无效的表名: {table_name}")
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', field_name):
            raise ValueError(f"无效的字段名: {field_name}")
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', id_field):
            raise ValueError(f"无效的ID字段名: {id_field}")
        
        # 验证记录ID
        try:
            record_id = str(record_id)
        except (ValueError, TypeError):
            raise ValueError(f"记录ID转换失败: {record_id}")
        
        # 构建SQL语句
        if text_content is None:
            sql = f"UPDATE {table_name} SET {field_name} = NULL WHERE {id_field} = '{record_id}'"
        else:
            # 转义单引号防止SQL注入
            escaped_text = str(text_content).replace("'", "''")
            sql = f"UPDATE {table_name} SET {field_name} = '{escaped_text}' WHERE {id_field} = '{record_id}'"
        
        await execute_pro2_query_async(sql, charset=charset)
        logger.info(f"成功更新 {table_name}.{field_name}, ID={record_id}")
        return True
        
    except Exception as e:
        logger.error(f"更新BLOB字段失败: table={table_name}, field={field_name}, id={record_id}, error={e}")
        return False

async def update_company_address(company_code, address_text):
    """
    更新公司地址字段 - 特化版本
    
    Args:
        company_code: 公司代码
        address_text: 地址文本
        
    Returns:
        bool: 是否成功更新
    """
    return await update_blob_text_field_safe(
        table_name='company',
        field_name='address', 
        id_field='code',
        record_id=company_code,
        text_content=address_text
    )

async def update_company_remarks(company_code, remarks_text):
    """
    更新公司备注字段 - 特化版本
    
    Args:
        company_code: 公司代码
        remarks_text: 备注文本
        
    Returns:
        bool: 是否成功更新
    """
    return await update_blob_text_field_safe(
        table_name='company',
        field_name='remarks', 
        id_field='code',
        record_id=company_code,
        text_content=remarks_text
    )

async def update_invoice_internal_remarks(inv_cn_id, remarks_text):
    """
    更新发票内部备注字段 - 特化版本
    
    Args:
        inv_cn_id: 发票ID
        remarks_text: 备注文本
        
    Returns:
        bool: 是否成功更新
    """
    return await update_blob_text_field_safe(
        table_name='INVOICE_COST_NOTE',
        field_name='INTERNAL_REMARKS', 
        id_field='ID',
        record_id=inv_cn_id,
        text_content=remarks_text
    )