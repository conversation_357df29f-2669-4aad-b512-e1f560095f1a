#!/usr/bin/env python3
"""
优化的数据导出工具
支持Job和Booking数据导出为Excel和CSV格式，使用列映射配置
"""

import pandas as pd
from io import BytesIO, String<PERSON>
from typing import List, Dict, Union, Any
from datetime import datetime
import re
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, NamedStyle, PatternFill
from utils.basic.oss2_dwz import upload_get_dwz_url
from utils.basic.logger_config import setup_logger

# 配置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

# Job数据字段映射（根据PostgreSQL表结构修正）
JOB_COLUMN_MAPPING = {
    # 基本信息
    'job_type_cn': '业务类型',
    'job_date': '工作档日期', 
    'job_no': '工作档编号',

    # 船期信息
    'vessel': '船名',
    'voyage': '航次',
    'pol_code': '起运港',
    'pod_code': '卸货港',
    'etd_date': 'ETD日期',
    'eta_date': 'ETA日期',

    # 票数和重量统计
    'bk_count': '订舱数',
    'bill_count': '提单数',
    'rt': '计费吨',
    'teu': 'TEU',
    'nomi_count': '指定货票数',
    'nomi_rt': '指定货RT',

    # 财务信息
    'income': '收入',
    'cost': '成本',
    'profit': '利润',
    'transhipment_profit': '转运利润',
    'total_business_profit': '总业务利润',

    # 操作信息
    'operator_name': '操作员',
    'operator_department': '操作部门',
    'job_handling_agent': '工作档代理',

    # 集拼信息
    'is_consolidation': '是否集拼',
    'consolidation_20': '20集拼量',
    'consolidation_40': '40集拼量',

    # 状态信息
    'is_op_finished': '操作完成',
    'is_checked': '审核状态',
    
    # 系统信息
    'pro2_system_id': '系统ID',
    'session_id': '会话ID',
    'analysis_timestamp': '分析时间戳',
    'created_at': '创建时间',
    'job_id': '工作档ID',
    'job_type_id': '业务类型ID',
    'operator_id': '操作员ID',
    'data_hash': '数据哈希'
}

# Job数据导出字段顺序（根据PostgreSQL表结构修正）
JOB_EXPORT_ORDER = [
    'job_type_cn',        # 业务类型
    'job_date',           # 工作档日期
    'job_no',             # 工作档编号
    'vessel',             # 船名
    'voyage',             # 航次
    'pol_code',           # 起运港
    'pod_code',           # 卸货港
    'etd_date',           # ETD日期
    'eta_date',           # ETA日期
    'bk_count',           # 订舱数
    'bill_count',         # 提单数
    'rt',                 # 计费吨
    'teu',                # TEU
    'nomi_count',         # 指定货票数
    'nomi_rt',            # 指定货RT
    'income',             # 收入
    'cost',               # 成本
    'profit',             # 利润
    'transhipment_profit', # 转运利润
    'total_business_profit', # 总业务利润
    'operator_name',      # 操作员
    'operator_department', # 操作部门
    'job_handling_agent', # 工作档代理
    'is_consolidation',   # 是否集拼
    'consolidation_20',   # 20集拼量
    'consolidation_40',   # 40集拼量
    'is_op_finished',     # 操作完成
    'is_checked'          # 审核状态
]

# Booking数据字段映射（根据PostgreSQL表结构修正）
BOOKING_COLUMN_MAPPING = {
    # 基本信息
    'job_type_cn': '业务类型',
    'job_date': '工作档日期',
    'job_no': '工作档编号',
    'bkbl_no': '订舱提单编号',
    'client_name': '客户名称',

    # 船期信息
    'vessel': '船名',
    'voyage': '航次',
    'job_pol': '航次始发港',
    'bill_pol': '提单起运地',
    'bill_pod': '提单卸货地',

    # 服务模式和重量
    'service_mode': '服务模式',
    'lcl_rt': '拼箱RT',
    'teu': 'TEU',
    'air_weight': '空运重量',

    # 财务信息
    'income': '收入',
    'cost': '成本',
    'profit': '利润',
    'transhipment_profit': '转运利润',
    'total_business_profit': '总业务利润',

    # 转运信息
    'is_transhipment': '是否转运',
    'transhipment_id': '转运ID',

    # 业务信息
    'is_freehand': '自揽货',
    'salesman_name': '业务员',
    'salesman_department': '营业员部门',
    'nomi_agent_name': '指定货代理',
    'salesman_id': '业务员ID',

    # 操作信息
    'operator_name': '操作员',
    'operator_department': '操作部门',
    'coloader_name': 'Coloader名称',
    'job_handling_agent': '工作档代理',
    'bl_handling_agent': '提单代理',
    
    # 系统信息
    'pro2_system_id': '系统ID',
    'session_id': '会话ID',
    'analysis_timestamp': '分析时间戳',
    'created_at': '创建时间',
    'bkbl_id': '订舱提单ID',
    'job_id': '工作档ID',
    'job_type_id': '业务类型ID',
    'operator_id': '操作员ID',
    'data_hash': '数据哈希'
}

# Booking数据导出字段顺序（根据PostgreSQL表结构修正）
BOOKING_EXPORT_ORDER = [
    'job_type_cn',          # 业务类型
    'job_date',             # 工作档日期
    'job_no',               # 工作档编号
    'bkbl_no',              # 订舱提单编号
    'client_name',          # 客户名称
    'vessel',               # 船名
    'voyage',               # 航次
    'job_pol',              # 航次始发港
    'bill_pol',             # 提单起运地
    'bill_pod',             # 提单卸货地
    'service_mode',         # 服务模式
    'lcl_rt',               # 拼箱RT
    'teu',                  # TEU
    'air_weight',           # 空运重量
    'income',               # 收入
    'cost',                 # 成本
    'profit',               # 利润
    'transhipment_profit',  # 转运利润
    'total_business_profit', # 总业务利润
    'is_transhipment',      # 是否转运
    'is_freehand',          # 自揽货
    'salesman_name',        # 业务员
    'salesman_department',  # 营业员部门
    'nomi_agent_name',      # 指定货代理
    'operator_name',        # 操作员
    'operator_department',  # 操作部门
    'coloader_name',        # Coloader名称
    'job_handling_agent',   # 工作档代理
    'bl_handling_agent'     # 提单代理
]

def get_column_mapping(data_type: str) -> Dict[str, str]:
    """
    根据数据类型获取相应的列映射
    
    Args:
        data_type: 数据类型 ("job", "booking", "analysis")
        
    Returns:
        Dict[str, str]: 列映射字典
    """
    if data_type == "job":
        return JOB_COLUMN_MAPPING
    elif data_type == "booking":
        return BOOKING_COLUMN_MAPPING
    elif data_type == "analysis":
        # 对于分析类型，返回空映射，因为数据已经是中文列名
        return {}
    else:
        # 合并两个映射作为通用映射
        combined_mapping = {}
        combined_mapping.update(BOOKING_COLUMN_MAPPING)
        combined_mapping.update(JOB_COLUMN_MAPPING)
        return combined_mapping

def apply_column_mapping(data: List[Dict], data_type: str) -> List[Dict]:
    """
    应用列映射，将英文字段名转换为中文，并按指定顺序排列

    Args:
        data: 原始数据
        data_type: 数据类型

    Returns:
        List[Dict]: 转换后的数据
    """
    if not data:
        return data

    mapping = get_column_mapping(data_type)
    converted_data = []

    # 获取字段顺序
    if data_type == "job":
        field_order = JOB_EXPORT_ORDER
    elif data_type == "booking":
        field_order = BOOKING_EXPORT_ORDER
    elif data_type == "analysis":
        # 对于分析类型，使用数据中的字段顺序（因为已经是中文列名）
        if data:
            field_order = list(data[0].keys())
        else:
            field_order = []
    else:
        # 对于其他类型，保持原有逻辑
        field_order = list(mapping.keys())

    for record in data:
        converted_record = {}

        # 按指定顺序处理字段
        for field in field_order:
            if field in record:
                # 对于分析类型，字段已经是中文名，不需要映射
                if data_type == "analysis":
                    chinese_key = field
                    original_field = field  # 用于format_value的原始字段名
                else:
                    # 使用映射转换字段名
                    chinese_key = mapping.get(field, field)
                    original_field = field
                converted_record[chinese_key] = format_value(original_field, record[field])

        converted_data.append(converted_record)

    return converted_data

def format_value(key: str, value: Any) -> Any:
    """
    格式化数据值
    
    Args:
        key: 字段名
        value: 原始值
        
    Returns:
        格式化后的值
    """
    if value is None:
        return ''
    
    # 数字字段处理
    numeric_fields = [
        'income', 'cost', 'profit', 'rt', 'lcl_rt', 'teu', 'air_weight',
        'transhipment_profit', 'total_business_profit',
        'bk_count', 'bill_count', 'nomi_count', 'nomi_rt',
        'consolidation_20', 'consolidation_40'
    ]
    
    # 日期字段处理
    date_fields = ['job_date', 'etd_date', 'eta_date']
    
    # 布尔字段处理
    boolean_fields = ['is_checked', 'is_op_finished', 'is_consolidation', 'is_transhipment', 'is_freehand']
    
    try:
        # 处理数字字段
        if key in numeric_fields:
            if isinstance(value, str):
                # 移除千分位分隔符和其他非数字字符
                cleaned_value = re.sub(r'[^\d\.-]', '', str(value))
                if cleaned_value and cleaned_value != '-':
                    value = float(cleaned_value)
                else:
                    return value
            
            if isinstance(value, (int, float)):
                # 如果是整数或整数的浮点数，不显示小数点
                if isinstance(value, float) and value.is_integer():
                    return int(value)
                # 如果是小数，保留2位小数
                elif isinstance(value, float):
                    return round(value, 2)
                else:
                    return value
        
        # 处理日期字段
        elif key in date_fields:
            if isinstance(value, str) and value.strip():
                # 尝试多种日期格式
                date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y-%m-%d %H:%M:%S']
                for fmt in date_formats:
                    try:
                        parsed_date = datetime.strptime(value.strip(), fmt)
                        return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
                # 如果所有格式都不匹配，返回原值
                return value
        
        # 处理布尔字段
        elif key in boolean_fields:
            if isinstance(value, (int, bool)):
                return "是" if value else "否"
            elif isinstance(value, str):
                return "是" if value.lower() in ['true', '1', 'yes', '是'] else "否"
        
        # 字符串字段：移除多余空格
        elif isinstance(value, str):
            return value.strip()
        
    except Exception:
        # 如果处理失败，返回原值
        pass
    
    return value

def create_excel_file(data: List[Dict], title: str, data_type: str, date_range: str = None) -> BytesIO:
    """
    创建Excel文件
    
    Args:
        data: 数据列表
        title: 文件标题
        data_type: 数据类型
        date_range: 日期范围，格式如"2024-01-01_2024-01-31"
        
    Returns:
        BytesIO: Excel文件的字节流
    """
    excel_buffer = BytesIO()
    
    # 应用列映射
    formatted_data = apply_column_mapping(data, data_type)
    
    if not formatted_data:
        formatted_data = [{"提示": "暂无数据"}]
    
    # 创建DataFrame
    df = pd.DataFrame(formatted_data)

    # 确保列顺序正确
    if formatted_data:
        chinese_columns = []
        if data_type == "job":
            export_order = JOB_EXPORT_ORDER
            column_mapping = JOB_COLUMN_MAPPING
        elif data_type == "booking":
            export_order = BOOKING_EXPORT_ORDER
            column_mapping = BOOKING_COLUMN_MAPPING
        elif data_type == "analysis":
            # 对于分析类型，使用DataFrame现有的列顺序
            export_order = list(df.columns) if not df.empty else []
            column_mapping = {}
        else:
            export_order = None
            column_mapping = get_column_mapping(data_type)

        if export_order:
            # 获取中文列名的顺序
            for field in export_order:
                chinese_name = column_mapping.get(field, field)
                if chinese_name in df.columns:
                    chinese_columns.append(chinese_name)

            # 重新排列DataFrame的列
            if chinese_columns:
                df = df[chinese_columns]
    
    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    
    # 设置工作表名称
    if date_range:
        sheet_name = f"{data_type}-{date_range.replace('_', '至')}"
    else:
        sheet_name = f"{data_type}-数据"
    
    # 确保工作表名称不超过31个字符（Excel限制）
    if len(sheet_name) > 31:
        sheet_name = sheet_name[:31]
    
    ws.title = sheet_name
    
    # 添加数据从第1行开始
    start_row = 1
    
    # 添加表头
    headers = list(df.columns)
    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=start_row, column=col_idx)
        cell.value = header
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # 定义样式
    number_style = NamedStyle(name="number_format", number_format="#,##0.00")
    integer_style = NamedStyle(name="integer_format", number_format="#,##0")
    date_style = NamedStyle(name="date_format", number_format="YYYY-MM-DD")
    
    # 注册样式（避免重复注册）
    try:
        wb.add_named_style(number_style)
        wb.add_named_style(integer_style)
        wb.add_named_style(date_style)
    except ValueError:
        # 样式已存在，忽略错误
        pass
    
    # 添加数据行
    for row_idx, row_data in enumerate(df.itertuples(index=False), start_row + 1):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            header = headers[col_idx - 1]
            
            # 设置值
            cell.value = value
            
            # 根据数据类型设置格式和对齐
            if is_numeric_value(value):
                if isinstance(value, float) and not value.is_integer():
                    cell.style = number_style
                else:
                    cell.style = integer_style
                cell.alignment = Alignment(horizontal="right", vertical="center")
            elif is_date_value(value):
                cell.style = date_style
                cell.alignment = Alignment(horizontal="center", vertical="center")
            else:
                cell.alignment = Alignment(horizontal="left", vertical="center")
    
    # 自动调整列宽
    for col in ws.columns:
        max_length = 0
        column_letter = col[0].column_letter
        
        for cell in col:
            try:
                if cell.value is not None:
                    cell_length = len(str(cell.value))
                    if cell_length > max_length:
                        max_length = cell_length
            except:
                pass
        
        # 设置合适的列宽（最小10，最大50）
        adjusted_width = min(max(max_length + 2, 10), 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # 不需要合并标题单元格，因为没有标题行
    
    # 保存到BytesIO
    wb.save(excel_buffer)
    excel_buffer.seek(0)
    excel_buffer.name = f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    return excel_buffer

def create_csv_file(data: List[Dict], title: str, data_type: str) -> BytesIO:
    """
    创建CSV文件
    
    Args:
        data: 数据列表
        title: 文件标题
        data_type: 数据类型
        
    Returns:
        BytesIO: CSV文件的字节流
    """
    csv_buffer = BytesIO()
    
    # 应用列映射
    formatted_data = apply_column_mapping(data, data_type)
    
    if not formatted_data:
        formatted_data = [{"提示": "暂无数据"}]
    
    # 创建DataFrame
    df = pd.DataFrame(formatted_data)

    # 确保列顺序正确
    if formatted_data:
        chinese_columns = []
        if data_type == "job":
            export_order = JOB_EXPORT_ORDER
            column_mapping = JOB_COLUMN_MAPPING
        elif data_type == "booking":
            export_order = BOOKING_EXPORT_ORDER
            column_mapping = BOOKING_COLUMN_MAPPING
        elif data_type == "analysis":
            # 对于分析类型，使用DataFrame现有的列顺序
            export_order = list(df.columns) if not df.empty else []
            column_mapping = {}
        else:
            export_order = None
            column_mapping = get_column_mapping(data_type)

        if export_order:
            # 获取中文列名的顺序
            if data_type == "analysis":
                # 分析类型直接使用列名
                chinese_columns = [col for col in export_order if col in df.columns]
            else:
                # 其他类型使用映射
                chinese_columns = []
                for field in export_order:
                    chinese_name = column_mapping.get(field, field)
                    if chinese_name in df.columns:
                        chinese_columns.append(chinese_name)

            # 重新排列DataFrame的列
            if chinese_columns:
                df = df[chinese_columns]
    
    # 创建CSV内容
    csv_content = StringIO()
    
    # 将DataFrame转换为CSV
    df.to_csv(csv_content, index=False)
    
    # 将字符串内容转换为字节流，使用UTF-8编码
    csv_buffer.write(csv_content.getvalue().encode('utf-8-sig'))
    csv_buffer.seek(0)
    csv_buffer.name = f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    return csv_buffer

def is_numeric_value(value) -> bool:
    """判断是否为数字值"""
    if isinstance(value, (int, float)):
        return True
    if isinstance(value, str):
        try:
            float(value)
            return True
        except ValueError:
            return False
    return False

def is_date_value(value) -> bool:
    """判断是否为日期值"""
    if isinstance(value, str):
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'\d{4}-\d{1,2}-\d{1,2}',
            r'\d{4}/\d{1,2}/\d{1,2}'
        ]
        return any(re.match(pattern, value.strip()) for pattern in date_patterns)
    return False

async def export_to_oss(data: Union[List[Dict], Dict], filename_prefix: str, data_type: str, file_format: str) -> str:
    """
    将数据导出为指定格式并上传到OSS
    
    Args:
        data: 数据（列表或包含data字段的字典）
        filename_prefix: 文件名前缀
        data_type: 数据类型 ("job" 或 "booking")
        file_format: 文件格式 ("excel" 或 "csv")
        
    Returns:
        str: OSS下载链接
    """
    try:
        # 提取数据
        if isinstance(data, dict) and 'data' in data:
            export_data = data['data']
            total_count = data.get('total_count', len(export_data) if export_data else 0)
        else:
            export_data = data if isinstance(data, list) else []
            total_count = len(export_data)
        
        if not export_data:
            logger.warning(f"导出数据为空: {filename_prefix}")
            export_data = [{"提示": "暂无数据"}]
        
        # 生成标题
        type_name = "工作档明细" if data_type == "job" else "订舱明细"
        title = f"{type_name}数据_{filename_prefix}_{total_count}条记录"
        
        # 从filename_prefix提取日期范围
        date_range = None
        if "_" in filename_prefix:
            # filename_prefix格式如"bookings_2024-01-01_2024-01-31"
            parts = filename_prefix.split("_")
            if len(parts) >= 3:
                date_range = f"{parts[1]}_{parts[2]}"
        
        # 根据格式创建文件
        if file_format.lower() == "excel":
            file_buffer = create_excel_file(export_data, title, data_type, date_range)
        else:
            file_buffer = create_csv_file(export_data, title, data_type)
        
        # 上传到OSS并获取下载链接
        # logger.info(f"开始上传{file_format.upper()}文件到OSS: {title}")
        download_url = upload_get_dwz_url(file_buffer, need_dwz=True)
        # logger.info(f"{file_format.upper()}文件上传成功: {download_url}")
        
        return download_url
        
    except Exception as e:
        logger.error(f"导出{file_format.upper()}文件失败: {str(e)}")
        raise Exception(f"导出{file_format.upper()}文件失败: {str(e)}")

async def export_both_formats(data: Union[List[Dict], Dict], filename_prefix: str, data_type: str) -> Dict[str, str]:
    """
    将数据同时导出为Excel和CSV格式
    
    Args:
        data: 数据
        filename_prefix: 文件名前缀
        data_type: 数据类型
        
    Returns:
        Dict[str, str]: 包含两种格式下载链接的字典
    """
    try:
        # 并行导出两种格式
        import asyncio
        excel_task = export_to_oss(data, filename_prefix, data_type, "excel")
        csv_task = export_to_oss(data, filename_prefix, data_type, "csv")
        
        excel_url, csv_url = await asyncio.gather(excel_task, csv_task)
        
        return {
            "excel_url": excel_url,
            "csv_url": csv_url,
            "excel_format": "Excel格式 (.xlsx)",
            "csv_format": "CSV格式 (.csv)"
        }
        
    except Exception as e:
        logger.error(f"同时导出Excel和CSV失败: {str(e)}")
        raise Exception(f"同时导出Excel和CSV失败: {str(e)}")