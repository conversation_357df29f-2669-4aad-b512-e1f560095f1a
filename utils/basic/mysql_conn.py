# -*- coding: utf-8 -*-
# MySQL数据库连接模块

import os
import functools
import time
import socket
import asyncio
import atexit
from typing import Dict, List, Union
from random import uniform
from contextlib import contextmanager, asynccontextmanager
from datetime import datetime, date
import pandas as pd
import pymysql
from pymysql.cursors import DictCursor
from dotenv import load_dotenv
import aiomysql
from utils.basic.logger_config import setup_logger

# 加载环境变量
load_dotenv(override=True)

# 使用统一的日志配置
logger = setup_logger(__name__, level="warning")

# 重试装饰器
def retry_on_connection_error(max_attempts=3, delay=1.0, backoff=2.0):
    """
    连接重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避系数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except (BrokenPipeError, ConnectionError, 
                        pymysql.OperationalError,
                        pymysql.InterfaceError,
                        socket.error, OSError) as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        # 对于recv相关错误，增加等待时间
                        if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                            wait_time = delay * (backoff ** attempt) + uniform(1.0, 2.0)
                            logger.warning(
                                "[RETRY] %s 网络接收错误 (尝试 %d/%d): %s, %.2f秒后重试",
                                func.__name__, attempt + 1, max_attempts, e, wait_time
                            )
                        else:
                            wait_time = delay * (backoff ** attempt) + uniform(0, 0.1)
                            logger.warning(
                                "[RETRY] %s 连接失败 (尝试 %d/%d): %s, %.2f秒后重试",
                                func.__name__, attempt + 1, max_attempts, e, wait_time
                            )
                        time.sleep(wait_time)
                    else:
                        logger.error("[RETRY] %s 重试%d次后仍然失败: %s", func.__name__, max_attempts, e)
                        break
                except Exception as e:
                    # 非连接相关错误直接抛出
                    logger.error("[RETRY] %s 非连接错误: %s", func.__name__, e)
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        return wrapper
    return decorator

# MySQL数据库配置
MYSQL_HOST = os.getenv("MYSQL_HOST")
MYSQL_USER = os.getenv("MYSQL_USER")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD")
MYSQL_DB_CMSCHINA = os.getenv("MYSQL_DB_CMSCHINA", "cmschina_aid")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", 3306))


class DatabaseError(Exception):
    """自定义数据库异常类"""
    pass


@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def connect_mysql(host: str = None, port: int = None, database: str = None, connect_timeout: int = None) -> pymysql.Connection:
    """连接MySQL数据库"""
    # 使用提供的参数或默认值
    if host is None:
        host = MYSQL_HOST
    if port is None:
        port = MYSQL_PORT
    if database is None:
        database = MYSQL_DB_CMSCHINA  # 默认数据库
    if connect_timeout is None:
        connect_timeout = int(os.getenv('MYSQL_CONNECT_TIMEOUT', '10'))
    
    logger.debug("[MYSQL_CONN] Connecting to MySQL: %s:%d/%s", host, port, database)
    
    try:
        # 增强MySQL连接参数以提高稳定性
        return pymysql.connect(
            host=host,
            port=port,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            database=database,
            cursorclass=DictCursor,
            connect_timeout=connect_timeout or 600,  # 连接超时600秒
            read_timeout=600,  # 读取超时600秒
            write_timeout=600,  # 写入超时600秒
            charset='utf8mb4',  # 使用utf8mb4字符集
            autocommit=False,  # 关闭自动提交
            sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO',
            init_command="SET SESSION wait_timeout=600, interactive_timeout=600"  # 设置会话超时
        )
    except Exception as e:
        raise DatabaseError(f"MySQL连接失败: {str(e)}") from e


@contextmanager
def get_mysql_connection(database: str = None):
    """MySQL连接上下文管理器"""
    conn = None
    try:
        conn = connect_mysql(database=database)
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise DatabaseError(f"MySQL操作失败: {str(e)}")
    finally:
        if conn:
            conn.close()


@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def execute_mysql_query(
    query: str,
    params: tuple = None,
    fetch_all: bool = False,
    auto_commit: bool = True,
    return_last_id: bool = False,
    database: str = None
) -> Union[Dict, List[Dict], int, None]:
    """执行MySQL查询"""
    conn = None
    cursor = None
    try:
        conn = connect_mysql(database=database)
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if return_last_id:
            # MySQL使用LAST_INSERT_ID()获取最后插入的ID
            last_id = cursor.lastrowid
            if auto_commit:
                conn.commit()
            return last_id
        
        query_upper = query.strip().upper()
        if query_upper.startswith('SELECT') or query_upper.startswith('SHOW') or query_upper.startswith('DESCRIBE'):
            result = cursor.fetchall() if fetch_all else cursor.fetchone()
        else:
            result = cursor.rowcount
        
        if auto_commit:
            conn.commit()
        
        return result
        
    except Exception as e:
        if conn and auto_commit:
            try:
                conn.rollback()
            except Exception:
                pass
        raise DatabaseError(f"MySQL查询执行错误: {str(e)}") from e
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


# 异步支持函数
async def execute_mysql_query_async(
    query: str,
    params: tuple = None,
    fetch_all: bool = False,
    auto_commit: bool = True,
    return_last_id: bool = False,
    database: str = None
):
    """异步执行MySQL查询"""
    return await asyncio.to_thread(
        execute_mysql_query, query, params, fetch_all, auto_commit, return_last_id, database
    )

@asynccontextmanager
async def get_mysql_connection_async(database: str = None):
    """异步MySQL连接上下文管理器"""
    # 使用默认连接参数
    host = MYSQL_HOST
    port = MYSQL_PORT
    if database is None:
        database = MYSQL_DB_CMSCHINA  # 默认数据库
    
    logger.debug("[ASYNC_MYSQL_CONN] Connecting to MySQL: %s:%d/%s", host, port, database)
    
    conn = None
    try:
        # 创建异步MySQL连接
        logger.debug(
            "[ASYNC_MYSQL_CONN] 尝试连接参数: host=%s, port=%d, user=%s, database=%s",
            host, port, MYSQL_USER, database
        )
        conn = await aiomysql.connect(
            host=host,
            port=port,
            user=MYSQL_USER,
            password=MYSQL_PASSWORD,
            db=database,
            charset='utf8mb4',
            autocommit=False,
            connect_timeout=600,  # 连接超时600秒
            read_timeout=600,     # 读取超时600秒
            write_timeout=600     # 写入超时600秒
        )
        yield conn
    except Exception as e:
        logger.error("[ASYNC_MYSQL_CONN] MySQL连接失败: %s", e)
        logger.error("[ASYNC_MYSQL_CONN] 异常类型: %s", type(e))
        import traceback
        logger.error("[ASYNC_MYSQL_CONN] 详细堆栈: %s", traceback.format_exc())
        raise DatabaseError(f"异步MySQL连接失败: {str(e)}") from e
    finally:
        if conn:
            conn.close()

# 清理函数
def cleanup_connections():
    """清理所有连接"""
    logger.info("[CLEANUP] Starting connection cleanup")
    # 注意：现在没有需要清理的持久连接，但保留此函数以保持API兼容性
    logger.info("[CLEANUP] Connection cleanup completed")

# 安全格式化日期
def safe_format_date(date_value, format_str='%Y-%m-%d'):
    """安全格式化日期，处理各种日期类型"""
    
    if date_value is None:
        return None
    
    # 检查是否为pandas NaN
    try:
        if pd.isna(date_value):
            return None
    except ImportError:
        # 如果没有pandas，简单检查字符串是否为'nan'或'NaN'
        if isinstance(date_value, str) and date_value.lower() in ['nan', 'nat']:
            return None
    
    # 如果已经是字符串，直接返回（假设已经是正确格式）
    if isinstance(date_value, str):
        # 尝试解析字符串日期并重新格式化
        try:
            # 常见的日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S'
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_value.strip(), fmt)
                    return parsed_date.strftime(format_str)
                except ValueError:
                    continue
            
            # 如果无法解析，返回原字符串
            return date_value
        except Exception:
            return date_value
    
    # 如果是datetime或date对象
    if isinstance(date_value, (datetime, date)):
        try:
            return date_value.strftime(format_str)
        except Exception:
            return str(date_value)
    
    # 如果是pandas的Timestamp
    if hasattr(date_value, 'strftime'):
        try:
            return date_value.strftime(format_str)
        except Exception:
            return str(date_value)
    
    # 其他情况，尝试转换为字符串
    return str(date_value) if date_value else None

def execute_mysql_script(
    script: str,
    database: str = None,
    auto_commit: bool = True
) -> bool:
    """执行MySQL脚本（多条SQL语句）"""
    
    with get_mysql_connection(database=database) as conn:
        with conn.cursor() as cursor:
            try:
                # MySQL需要逐条执行SQL语句
                statements = script.split(';')
                for statement in statements:
                    statement = statement.strip()
                    if statement:  # 跳过空语句
                        cursor.execute(statement)
                
                if auto_commit:
                    conn.commit()
                logger.info("MySQL脚本执行成功")
                return True
            except Exception as e:
                if auto_commit:
                    conn.rollback()
                raise DatabaseError(f"MySQL脚本执行错误: {str(e)}")


def test_mysql_connection(database: str = None) -> bool:
    """测试MySQL连接"""
    try:
        with get_mysql_connection(database=database) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT VERSION();")
                version = cursor.fetchone()
                logger.info(f"MySQL连接测试成功: {version['VERSION()']}")
                return True
    except Exception as e:
        logger.error(f"MySQL连接测试失败: {str(e)}")
        return False


def get_table_exists(table_name: str, schema: str = None, database: str = None) -> bool:
    """检查表是否存在"""
    if schema is None:
        schema = database or MYSQL_DB_CMSCHINA
    
    query = """
        SELECT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = %s 
            AND table_name = %s
        ) as table_exists;
    """
    
    try:
        result = execute_mysql_query(
            query, 
            params=(schema, table_name), 
            database=database
        )
        return bool(result['table_exists']) if result else False
    except Exception as e:
        logger.error(f"检查表存在性失败: {str(e)}")
        return False


def drop_table_if_exists(table_name: str, schema: str = None, database: str = None) -> bool:
    """删除表（如果存在）"""
    if schema is None:
        schema = database or MYSQL_DB_CMSCHINA
    
    query = f"DROP TABLE IF EXISTS `{schema}`.`{table_name}`;"
    
    try:
        execute_mysql_query(query, database=database)
        logger.info(f"表 {schema}.{table_name} 删除成功")
        return True
    except Exception as e:
        logger.error(f"删除表失败: {str(e)}")
        return False

atexit.register(cleanup_connections)

# 导出常用函数
__all__ = [
    'DatabaseError',
    'connect_mysql',
    'get_mysql_connection',
    'get_mysql_connection_async',
    'execute_mysql_query',
    'execute_mysql_query_async',
    'execute_mysql_script',
    'test_mysql_connection',
    'get_table_exists',
    'drop_table_if_exists',
    'cleanup_connections',
    'safe_format_date',
    'MYSQL_DB_CMSCHINA'
]