import os
import json
from decimal import Decimal
from datetime import datetime, date, timedelta
import smtplib
from email.mime.text import MIMEText
from email.header import Header
import asyncio
from utils.basic.mysql_conn import execute_mysql_query, execute_mysql_query_async
from utils.basic.fb_conn import execute_pro2_query, execute_pro2_query_async
from utils.basic.logger_config import setup_logger
from dotenv import load_dotenv

load_dotenv(override=True)

logger = setup_logger(__name__)

# 修改 DecimalEncoder 类以同时处理 Decimal 和 date 类型
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, (datetime, date)):  # 添加对日期类型的支持
            return obj.isoformat()
        return super(DecimalEncoder, self).default(obj)

# 添加计算mysql数据库表application_invoice_info人民币总金额的函数
def calculate_total_amount(application_id: int) -> tuple[Decimal, str]:
    """
    计算mysql数据库表application_invoice_info中指定币种的总金额
    
    Args:
        application_id (int): 申请ID
        
    Returns:
        tuple[Decimal, str]: 返回(目标币种总金额, 币种代码)
    
    Raises:
        ValueError: 当申请ID不存在或数据格式无效时
        TypeError: 当参数类型不正确时
    """
    if not isinstance(application_id, int) or application_id <= 0:
        raise TypeError("application_id必须是正整数")
    
    # 获取application_invoice_info表中的charges字段
    sql = """
        SELECT charges, exchange_rates, currency_code
        FROM application_invoice_info
        WHERE id = %s
    """
    
    try:
        result = execute_mysql_query(sql, (application_id,), fetch_all=False)
        if not result:
            raise ValueError(f"未找到申请ID为 {application_id} 的记录")
        
        charges_str = result.get('charges', '')
        exchange_rates_str = result.get('exchange_rates', '')
        currency_code_ttl = result.get('currency_code', '')

        # 解析JSON数据
        try:
            charges = json.loads(charges_str) if charges_str else []
            exchange_rates = json.loads(exchange_rates_str) if exchange_rates_str else []
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误 (application_id={application_id}): {e}")
            raise ValueError(f"无效的JSON格式: {e}")

        if not isinstance(charges, list):
            raise ValueError("charges字段必须是列表格式")
        
        total_amount = Decimal('0')
        
        for i, charge in enumerate(charges):
            if not isinstance(charge, dict):
                logger.warning(f"跳过无效的费用记录 (索引={i}): {charge}")
                continue
                
            try:
                amount = Decimal(str(charge.get('amount', 0)))
                currency_code = charge.get('currency_code', '')
                
                # 如果币种与目标币种不同且有汇率信息，进行转换
                if currency_code != currency_code_ttl and exchange_rates:
                    rate = next(
                        (r for r in exchange_rates if
                         (r.get('left_code') == currency_code and r.get('right_code') == currency_code_ttl) or
                         (r.get('right_code') == currency_code and r.get('left_code') == currency_code_ttl)),
                        None
                    )
                    
                    if rate:
                        try:
                            if rate.get('left_code') == currency_code:
                                rate_multiplier = (Decimal(str(rate.get('right_rate', 1))) / 
                                                 Decimal(str(rate.get('left_rate', 1))))
                            else:
                                rate_multiplier = (Decimal(str(rate.get('left_rate', 1))) / 
                                                 Decimal(str(rate.get('right_rate', 1))))
                            amount = amount * rate_multiplier
                        except (ValueError, TypeError, ZeroDivisionError) as rate_error:
                            logger.warning(f"汇率转换失败 (费用索引={i}): {rate_error}")
                            # 继续使用原金额，不进行转换
                
                total_amount += amount
                
            except (ValueError, TypeError, AttributeError) as amount_error:
                logger.warning(f"处理费用记录失败 (索引={i}): {amount_error}")
                continue

        return round(total_amount, 2), currency_code_ttl
        
    except Exception as e:
        logger.error(f"计算总金额失败 (application_id={application_id}): {e}")
        raise

def get_pro2_inv_cn_total_amount(application_id: int) -> tuple[Decimal, str]:
    """
    获取pro2账单总金额
    
    Args:
        application_id (int): 申请ID
        
    Returns:
        tuple[Decimal, str]: 返回(总金额, 币种代码)
        
    Raises:
        ValueError: 当申请ID不存在或发票ID无效时
        TypeError: 当参数类型不正确时
    """
    if not isinstance(application_id, int) or application_id <= 0:
        raise TypeError("application_id必须是正整数")
    
    try:
        # 获取inv_cn_id
        sql_inv_cn_id = """
            SELECT inv_cn_id
            FROM application_invoice_info
            WHERE id = %s
        """
        result = execute_mysql_query(sql_inv_cn_id, (application_id,), fetch_all=False)
        if not result:
            raise ValueError(f"未找到申请ID为 {application_id} 的记录")
        
        inv_cn_id = result.get('inv_cn_id')
        if not inv_cn_id:
            raise ValueError(f"申请ID {application_id} 的发票ID为空")
        
        # 获取pro2发票总金额
        sql_inv_cn_total_amount = """
            SELECT total_amount, currency_code
            FROM invoice_cost_note
            WHERE id = ?
        """
        result = execute_pro2_query(sql_inv_cn_total_amount, (inv_cn_id,), charset='UTF-8')
        if not result:
            raise ValueError(f"未找到发票ID为 {inv_cn_id} 的记录")
        
        try:
            local_amount = Decimal(str(result[0])).quantize(Decimal('0.01'))
            currency_code_ttl = str(result[1]) if result[1] else ''
        except (ValueError, IndexError) as e:
            raise ValueError(f"无效的发票金额数据 (inv_cn_id={inv_cn_id}): {e}")

        return local_amount, currency_code_ttl
        
    except Exception as e:
        logger.error(f"获取Pro2发票总金额失败 (application_id={application_id}): {e}")
        raise

# 异步版本的函数，提高性能
async def calculate_total_amount_async(application_id: int) -> tuple[Decimal, str]:
    """
    异步计算mysql数据库表application_invoice_info中指定币种的总金额
    
    Args:
        application_id (int): 申请ID
        
    Returns:
        tuple[Decimal, str]: 返回(目标币种总金额, 币种代码)
    """
    return await asyncio.to_thread(calculate_total_amount, application_id)

async def get_pro2_inv_cn_total_amount_async(application_id: int) -> tuple[Decimal, str]:
    """
    异步获取pro2账单总金额
    
    Args:
        application_id (int): 申请ID
        
    Returns:
        tuple[Decimal, str]: 返回(总金额, 币种代码)
    """
    return await asyncio.to_thread(get_pro2_inv_cn_total_amount, application_id)

def get_update_inv_list():
    """
    获取需要处理的申请列表
    is_confirmed = 1 表示确认, 0 表示未确认, 2 表示拒绝, 3 表示撤回
    is_completed = 0 表示未完成, 1 表示完成, 2 表示出现错误
    is_uipath_completed = 0 表示未完成, 1 表示完成, 2 表示出现错误
    """
    # 首先检查application_invoice_info表中is_new_invoice字段是否为1, inv_cn_id字段不为空的，设置is_uipath_completed为2, is_completed为2, completed_datetime为当前时间
    sql_select_inv_list = """
        select id
        from application_invoice_info
        where is_new_invoice = 1 and (inv_cn_id is not null and inv_cn_id != '' and inv_cn_id != 0) and (is_uipath_completed = 0 or is_completed = 0)
    """
    result_dict = execute_mysql_query(sql_select_inv_list, fetch_all=True)

    for inv in result_dict:
        id = inv.get('id')
        sql_update_inv_list = """
            update application_invoice_info
            set is_uipath_completed = 2, is_completed = 2, completed_datetime = %s, final_check_remarks = '自动录入发生错误,请注意检查Pro2系统, 并联系管理员。'
            where id = %s
        """
        execute_mysql_query(sql_update_inv_list, (datetime.now(), id))

    sql = """
        select 
            *
        from application_invoice_info
        where
            is_confirmed = 1 and
            is_completed = 0 and
            is_uipath_completed = 0
        order by application_datetime
    """
    result_dict = execute_mysql_query(sql, fetch_all=True)

    return json.dumps(result_dict, cls=DecimalEncoder, ensure_ascii=False)

def get_job_file_no(job_file_id):
    sql = """
        select 
            job_file_no
        from 
            job_file
        where 
            id = ?
    """
    result = execute_pro2_query(sql, (job_file_id,), charset='UTF-8')
    return result[0] if result else None

def update_blob_text_field_safe(table_name, field_name, id_field, record_id, text_content, charset='UTF-8'):
    """
    通用的 BLOB SUB_TYPE TEXT 字段安全更新函数
    
    解决 firebirdsql Python 驱动程序不支持参数化查询向 BLOB 字段写入的问题
    
    Args:
        table_name: 表名
        field_name: BLOB 字段名
        id_field: 主键字段名
        record_id: 记录ID
        text_content: 要写入的文本内容（None表示设置为NULL）
        charset: 字符编码，默认UTF-8
        
    Returns:
        bool: 是否成功更新
        
    Security:
        - 防SQL注入：转义单引号
        - 参数验证：验证表名、字段名格式
        - 整数验证：确保ID为整数
        
    Usage:
        # 更新发票内部备注
        update_blob_text_field_safe('INVOICE_COST_NOTE', 'INTERNAL_REMARKS', 'ID', 123, '备注内容')
        
        # 设置为NULL
        update_blob_text_field_safe('INVOICE_COST_NOTE', 'INTERNAL_REMARKS', 'ID', 123, None)
    """
    try:
        # 参数验证
        if not table_name or not field_name or not id_field:
            raise ValueError("表名、字段名和ID字段不能为空")
        
        # 验证表名和字段名格式（防止SQL注入）
        import re
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', table_name):
            raise ValueError(f"无效的表名: {table_name}")
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', field_name):
            raise ValueError(f"无效的字段名: {field_name}")
        if not re.match(r'^[A-Za-z_][A-Za-z0-9_]*$', id_field):
            raise ValueError(f"无效的ID字段名: {id_field}")
        
        # 验证记录ID
        try:
            record_id = int(record_id)
        except (ValueError, TypeError):
            raise ValueError(f"记录ID必须是整数: {record_id}")
        
        # 构建SQL语句
        if text_content is None:
            sql = f"UPDATE {table_name} SET {field_name} = NULL WHERE {id_field} = {record_id}"
        else:
            # 转义单引号防止SQL注入（注意：不对反斜杠进行额外转义，因为会导致双重转义）
            escaped_text = str(text_content).replace("'", "''")
            sql = f"UPDATE {table_name} SET {field_name} = '{escaped_text}' WHERE {id_field} = {record_id}"
        
        execute_pro2_query(sql, charset=charset)
        logger.info(f"成功更新 {table_name}.{field_name}, ID={record_id}")
        return True
        
    except Exception as e:
        logger.error(f"更新BLOB字段失败: table={table_name}, field={field_name}, id={record_id}, error={e}")
        return False

def update_internal_remarks(inv_cn_id, remarks_text):
    """
    更新INTERNAL_REMARKS字段 - 特化版本
    
    这是基于通用函数的特化版本，保持向后兼容性
    
    Args:
        inv_cn_id: 发票ID
        remarks_text: 备注文本
        
    Returns:
        bool: 是否成功更新
    """
    return update_blob_text_field_safe(
        table_name='INVOICE_COST_NOTE',
        field_name='INTERNAL_REMARKS', 
        id_field='ID',
        record_id=inv_cn_id,
        text_content=remarks_text
    )

# 通过job_file_id和create_user_id获取create_date最晚的记录
def get_last_inv_cn_data(job_file_id, create_username):
    """
    通过job_file_id和create_username获取create_date最晚的记录
    
    Args:
        job_file_id: 工作档ID
        create_username: 创建用户名
        
    Returns:
        dict: 最新的发票记录，如果没有找到则返回None
    """
    # 首先通过username获取user_id
    sql_get_user_id = """
        select user_id
        from users
        where username = ?
    """
    user_result = execute_pro2_query(sql_get_user_id, (create_username,), charset='UTF-8')
    if not user_result:
        logger.warning(f"未找到用户名为 {create_username} 的用户")
        return None
    
    create_user_id = user_result[0]
    
    # 获取create_date最晚的记录
    sql_get_last_record = """
        select *
        from invoice_cost_note
        where job_file_id = ? and create_user_id = ?
        order by create_date desc
        rows 1
    """
    
    result = execute_pro2_query(sql_get_last_record, (job_file_id, create_user_id), charset='UTF-8')
    if not result:
        logger.warning(f"未找到job_file_id={job_file_id}, create_user_id={create_user_id}的记录")
        return None
    
    # 将结果转换为字典格式
    columns = [
        'ID', 'INV_CN_NO', 'REFERENCE_NO', 'COMPANY_NAME', 'COMPANY_NAME_UPPER', 
        'COMPANY_CODE', 'COMPANY_DETAILS', 'COMPANY_TEL', 'COMPANY_FAX', 'COMPANY_EMAIL',
        'COMPANY_ATTN', 'JOB_FILE_ID', 'IS_VALID', 'IS_MAIN_INTERNAL', 'INTERNAL_ID',
        'DOCUMENT_TYPE', 'ISSUE_DATE', 'JOB_DATE', 'PAYMENT_DATE', 'CURRENCY_CODE',
        'TOTAL_AMOUNT', 'LOCAL_AMOUNT', 'STATUS', 'OUTSTANDING', 'LOCAL_OUTSTANDING',
        'REMARKS', 'NO_OF_PRINT', 'NO_OF_FAX', 'NO_OF_EMAIL', 'NO_OF_PDF',
        'RECEIPT_FLAG', 'RECEIPT_AMOUNT_PAID', 'LOCAL_EXCHANGE_RATE_FROM', 
        'LOCAL_EXCHANGE_RATE_TO', 'IS_PRINT_EXCHANGE_RATE', 'CREATE_USER_ID',
        'CREATE_DATE', 'IS_CONFIRMED', 'FORMAT_ID', 'BANK_ACCOUNT_ID', 'IFFSI_FLAG',
        'IFFSI_AMOUNT_PAID', 'ETA_DATE', 'ON_BOARD_DATE', 'IS_OPERATION_VERIFIED',
        'INTERNAL_REMARKS', 'LOCKED', 'MANAGER_VERIFIED'
    ]
    
    if len(result) != len(columns):
        logger.warning(f"查询结果列数不匹配: 期望{len(columns)}列，实际{len(result)}列")
        return None
    
    record_dict = dict(zip(columns, result))
    
    return record_dict

def update_job_closing_date(update_inv_json_str, is_unlock = 0):
    select_sql = """
        select
            job_file_no
        from
            job_file
        where
            id = ?
    """
        
    update_sql = """
        update
            job_file
        set
            job_closing_date = ?,
            is_checked = ?,
            is_op_finished = ?
        where
            id = ?
    """

    try:
        # 默认为锁定工作档, 设定closing date为昨日
        job_closing_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        is_checked = 1
        is_op_finished = 1
        unlock_info = "工作档锁定"
        
        if is_unlock == 1:
            # 解锁工作档, 设定closing date为明日
            job_closing_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
            is_checked = 0
            is_op_finished = 0
            unlock_info = "工作档解锁"

        update_inv_json = json.loads(update_inv_json_str)

        for inv in update_inv_json:

            job_file_id = inv['job_file_id']
            result = execute_pro2_query(select_sql, (job_file_id,), charset='UTF-8')
            job_file_no = result[0] if result else None

            execute_pro2_query(update_sql, (job_closing_date, is_checked, is_op_finished, job_file_id), charset='UTF-8')
            logger.info("%s成功: 编号%s", unlock_info, job_file_no)

    except Exception as e:
        logger.error("更新失败: %s", e)

def update_sea_inv_cn_other_info(application_id: int):
    """
    更新海运发票相关信息
    """
    try:
        sql_select_application_invoice_info = """
            select 
                *
            from application_invoice_info
            where id = %s
        """
        result_application_invoice_info = execute_mysql_query(sql_select_application_invoice_info, (application_id,), fetch_all=False)
        if not result_application_invoice_info:
            raise ValueError("输入数据不能为空")

        job_file_id = result_application_invoice_info.get('job_file_id', 0)
        company_code = result_application_invoice_info.get('company_code', '')
        document_type = result_application_invoice_info.get('document_type', '')
        if not company_code:
            raise ValueError("输入数据不能为空")

        inv_cn_id = result_application_invoice_info.get('inv_cn_id', 0)
        if inv_cn_id:
            try:
                inv_cn_id = int(inv_cn_id)
            except (ValueError, TypeError):
                inv_cn_id = 0
        
        # 获取基本信息
        if not job_file_id:
            raise ValueError("无效的job_file_id")
        
        if not inv_cn_id:
            # 获取新建的inv_cn_id
            select_sql = """
                select first 1 id 
                from invoice_cost_note 
                where job_file_id = ? 
                    and company_code = ?
                    and document_type = ?
                order by id desc
            """
            
            if not all([job_file_id, company_code, document_type]):
                raise ValueError("缺少必要参数: job_file_id, company_code 或 document_type")
                
            result = execute_pro2_query(select_sql, (job_file_id, company_code, document_type), charset='UTF-8')
            
            if not result:
                raise ValueError(f"未找到匹配的发票记录: job_file_id={job_file_id}")
            
            inv_cn_id = int(result[0])

            # 踢球inv_cn_no插入到mysql数据库的表application_invoice_info
            sql_select_inv_cn_no = """
                select inv_cn_no
                from invoice_cost_note
                where id = ?
            """
            result = execute_pro2_query(sql_select_inv_cn_no, (inv_cn_id,), charset='UTF-8')
            inv_cn_no = result[0] if result else None
            
            sql_update_inv_cn_no = """
                update application_invoice_info
                set inv_cn_id = %s, inv_cn_no = %s
                where id = %s
            """
            execute_mysql_query(sql_update_inv_cn_no, (inv_cn_id, inv_cn_no, application_id))
            
        logger.info("开始更新发票记录: inv_cn_id=%s", inv_cn_id)

        try:
            # 更新汇率信息
            exchange_rates_str = result_application_invoice_info.get('exchange_rates', '[]')
            try:
                exchange_rates = json.loads(exchange_rates_str)
            except json.JSONDecodeError:
                logger.warning("汇率信息JSON解析失败，使用空列表")
                exchange_rates = []
            _update_exchange_rates(inv_cn_id, exchange_rates)
            
            # 更新提单信息
            selected_booking_ids_str = result_application_invoice_info.get('selected_booking_ids', '[]')
            try:
                selected_booking_ids = json.loads(selected_booking_ids_str)
            except json.JSONDecodeError:
                logger.warning("提单ID信息JSON解析失败，使用空列表")
                selected_booking_ids = []
            
            job_type_id = int(result_application_invoice_info.get('job_type_id', 0))
            _update_bl_info(inv_cn_id, job_file_id, job_type_id, selected_booking_ids)
            
            # 更新费用信息
            charges_str = result_application_invoice_info.get('charges', '[]')
            try:
                charges = json.loads(charges_str)
            except json.JSONDecodeError:
                logger.warning("费用信息JSON解析失败，使用空列表")
                charges = []
            _update_charge_info(inv_cn_id, job_file_id, job_type_id, charges)
            
            logger.info("发票记录更新完成: inv_cn_id=%s", inv_cn_id)
            
        except Exception as e:
            logger.error("更新失败: %s", str(e))
            raise
            
    except json.JSONDecodeError as e:
        logger.error("JSON解析错误: %s", str(e))
        raise ValueError("无效的JSON格式") from e
    except Exception as e:
        logger.error("更新过程发生错误: %s", str(e))
        raise

def _update_exchange_rates(inv_cn_id, exchange_rates):
    """更新汇率信息"""
    try:
        # 清空现有汇率
        sql_delete = "delete from inv_cn_exchange_rate where inv_cn_id = ?"
        execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')

        # 插入新汇率
        sql_insert = """
            insert into inv_cn_exchange_rate 
            (inv_cn_id, left_code, left_rate, right_code, right_rate) 
            values (?, ?, ?, ?, ?)
        """
        
        for rate in exchange_rates:
            if not all(k in rate for k in ['left_code', 'left_rate', 'right_code', 'right_rate']):
                logger.warning(f"汇率记录缺少必要字段: {rate}")
                continue
                
            execute_pro2_query(
                sql_insert, 
                (inv_cn_id, 
                 rate['left_code'], 
                 float(rate['left_rate']), 
                 rate['right_code'], 
                 float(rate['right_rate'])),
                charset='UTF-8'
            )
            
    except Exception as e:
        logger.error(f"更新汇率信息失败: {e}")
        raise

def _update_bl_info(inv_cn_id, job_file_id, job_type_id, selected_booking_ids):
    """更新提单信息"""
    try:
        if job_type_id == 1:
            # 海运出口
            sql_delete = "delete from inv_cn_sea_export_bk where inv_cn_id = ?"
            execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')
            
            sql_insert = "insert into inv_cn_sea_export_bk (inv_cn_id, bk_id) values (?, ?)"
            for bk_id in selected_booking_ids:
                try:
                    execute_pro2_query(sql_insert, (inv_cn_id, int(bk_id)), charset='UTF-8')
                except (ValueError, TypeError) as e:
                    logger.warning(f"无效的booking ID: {bk_id}, 错误: {e}")
                    continue

        elif job_type_id == 2:
            # 海运进口
            sql_delete = "delete from inv_cn_sea_import_bl where inv_cn_id = ?"
            execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')
            
            sql_insert = """
                insert into inv_cn_sea_import_bl 
                (inv_cn_id, job_file_id, bl_id) 
                values (?, ?, ?)
            """
            for bl_id in selected_booking_ids:
                try:
                    execute_pro2_query(sql_insert, (inv_cn_id, job_file_id, int(bl_id)), charset='UTF-8')
                except (ValueError, TypeError) as e:
                    logger.warning(f"无效的BL ID: {bl_id}, 错误: {e}")
                    continue

        elif job_type_id == 3:
            # 海运转运
            sql_delete = "delete from inv_cn_sea_switch_bl where inv_cn_id = ?"
            execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')
            
            sql_insert = "insert into inv_cn_sea_switch_bl (inv_cn_id, bl_id) values (?, ?)"
            for bl_id in selected_booking_ids:
                try:
                    execute_pro2_query(sql_insert, (inv_cn_id, int(bl_id)), charset='UTF-8')
                except (ValueError, TypeError) as e:
                    logger.warning(f"无效的BL ID: {bl_id}, 错误: {e}")
                    continue
                    
    except Exception as e:
        logger.error(f"更新提单信息失败: {e}")
        raise

def _update_charge_info(inv_cn_id, job_file_id, job_type_id, charges):
    """更新费用信息"""
    try:
        if job_type_id == 1:
            # 海运出口费用
            sql_delete = "delete from inv_cn_charge_sea_export_bl where inv_cn_id = ?"
            execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')

            for charge in charges:
                if not charge.get('bl_no'):
                    continue
                try:
                    bl_result = execute_pro2_query(
                        "select id from sea_export_bl where bl_no = ?", 
                        (charge['bl_no'],), charset='gbk'
                    )
                    if not bl_result:
                        logger.warning(f"未找到BL号: {charge['bl_no']}")
                        continue
                    bl_id = bl_result[0]
                    
                    execute_pro2_query(
                        """insert into inv_cn_charge_sea_export_bl 
                           (inv_cn_id, bl_id, line_no) values (?, ?, ?)""",
                        (inv_cn_id, bl_id, int(charge['line_no'])),
                        charset='UTF-8'
                    )
                except Exception as e:
                    logger.warning(f"处理出口费用记录失败: {charge}, 错误: {e}")
                    continue

        elif job_type_id == 2:
            # 海运进口费用
            sql_delete = "delete from inv_cn_charge_sea_import_bl where inv_cn_id = ?"
            execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')

            for charge in charges:
                if not charge.get('bl_no'):
                    continue
                try:
                    bl_result = execute_pro2_query(
                        "select id from sea_import_bl where bl_no = ? and job_file_id = ?",
                        (charge['bl_no'], job_file_id), charset='gbk'
                    )
                    if not bl_result:
                        logger.warning(f"未找到BL号: {charge['bl_no']}")
                        continue
                    bl_id = bl_result[0]
                    
                    execute_pro2_query(
                        """insert into inv_cn_charge_sea_import_bl 
                           (inv_cn_id, bl_id, line_no, job_file_id) values (?, ?, ?, ?)""",
                        (inv_cn_id, bl_id, int(charge['line_no']), job_file_id),
                        charset='UTF-8'
                    )
                except Exception as e:
                    logger.warning(f"处理进口费用记录失败: {charge}, 错误: {e}")
                    continue

        elif job_type_id == 3:
            # 海运转运费用
            sql_delete = "delete from inv_cn_charge_sea_switch_bl where inv_cn_id = ?"
            execute_pro2_query(sql_delete, (inv_cn_id,), charset='UTF-8')

            for charge in charges:
                if not charge.get('bl_no'):
                    continue
                try:
                    bl_result = execute_pro2_query(
                        "select id from sea_switch_bl where bl_no = ? and job_file_id = ?",
                        (charge['bl_no'], job_file_id), charset='gbk'
                    )
                    if not bl_result:
                        logger.warning(f"未找到BL号: {charge['bl_no']}")
                        continue
                    bl_id = bl_result[0]
                    
                    execute_pro2_query(
                        """insert into inv_cn_charge_sea_switch_bl 
                           (inv_cn_id, bl_id, line_no, job_file_id) values (?, ?, ?, ?)""",
                        (inv_cn_id, bl_id, int(charge['line_no']), job_file_id),
                        charset='UTF-8'
                    )
                except Exception as e:
                    logger.warning(f"处理转运费用记录失败: {charge}, 错误: {e}")
                    continue
                    
    except Exception as e:
        logger.error(f"更新费用信息失败: {e}")
        raise
  
def update_inv_cn_completed(application_id, is_uipath_completed = 1, final_check_remarks = '', is_confirmed = 1):
    """
    更新application_invoice_info表的状态字段
    参数说明：
    application_id: application_invoice_info表的id
    is_uipath_completed: 1表示完成，0表示未完成，2表示出现错误
    final_check_remarks: 最终检查备注
    is_confirmed: 确认状态 1表示确认，0表示未确认
    """
    # 首先验证application_invoice_info表中的inv_cn_id对应的币种、总金额和pro2账单是否一致
    local_amount_pro2, currency_code_ttl_pro2 = get_pro2_inv_cn_total_amount(application_id)
    total_amount_mysql, currency_code_ttl_mysql = calculate_total_amount(application_id)

    if (local_amount_pro2 != total_amount_mysql) or (currency_code_ttl_pro2 != currency_code_ttl_mysql):
        final_check_remarks = "自动录入发生币种或者基础货币金额不符,请注意检查Pro2系统。" + final_check_remarks
        is_uipath_completed = 2
        logger.info(f'录入基础货币金额不符，申请编号：{application_id}, Pro2系统账单金额：{local_amount_pro2}, 数据库金额：{total_amount_mysql}')

    time_now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    completed_datetime = time_now if is_uipath_completed in [1, 2] else None
    is_completed = is_uipath_completed if is_uipath_completed in [1, 2] else 0

    if final_check_remarks:
        sql = """
            update
                application_invoice_info
            set
                is_completed = %s,
                is_uipath_completed = %s,
                completed_datetime = %s,
                final_check_remarks = %s,
                is_confirmed = %s
            where
                id = %s
        """
        execute_mysql_query(sql, (is_completed, is_uipath_completed, completed_datetime, 
                                final_check_remarks, is_confirmed, application_id))
    else:
        sql = """
            update
                application_invoice_info
            set
                is_completed = %s,
                is_uipath_completed = %s,
                completed_datetime = %s,
                is_confirmed = %s
            where
                id = %s
        """
        execute_mysql_query(sql, (is_completed, is_uipath_completed, completed_datetime, 
                                is_confirmed, application_id))

def send_email_to_manager(application_id: int, application_datetime: str, job_file_no: str,
                          mail_content: str='以下Pro2账单申请Uipath处理出现错误', attn: str='管理员',
                          to_list: list=['<EMAIL>']):
    """
    发送邮件通知给管理员
    
    Args:
        application_id: 申请ID
        application_datetime: 申请时间
        job_file_no: 工作档编号
        to_list: 收件人列表
        attn: 收件人称呼
        mail_content: 邮件内容
    """
    # 邮件服务器配置
    smtp_server = os.getenv("SMTP_HOST", "smtphz.qiye.163.com")
    smtp_port = int(os.getenv("SMTP_PORT_SSL", 465))
    sender = os.getenv("EMAIL_USER_RPA01")
    password = os.getenv("EMAIL_PASSWORD_RPA01")
    
    # 收件人设置（这里可以根据需要修改）
    receivers = to_list
    
    # 构建邮件内容
    mail_content = f"""
    <p>尊敬的{attn}：</p>
    <p>{mail_content}：</p>
    <ul>
        <li>申请ID：{application_id}</li>
        <li>申请时间：{application_datetime}</li>
        <li>工作档编号：{job_file_no}</li>
    </ul>
    <p>请了解，谢谢！</p>
    <p>CMS RPA-01</p>
    """
    
    try:
        # 检查参数
        if not sender or not password:
            raise ValueError("邮件发送者或密码未配置")
        
        # 由于出现错误，需要将application_invoice_info表的is_uipath_completed字段设置为2
        sql = """
            update
                application_invoice_info
            set
                is_uipath_completed = 2
            where
                id = %s
        """
        execute_mysql_query(sql, (application_id,))

        # 创建邮件对象
        message = MIMEText(mail_content, 'html', 'utf-8')
        message['From'] = Header(f"CMS RPA-01 <{sender}>")
        message['To'] = Header("; ".join(receivers))  # 使用分号分隔多个收件人
        message['Subject'] = Header(f'Pro账单增改Uipath处理错误 - {job_file_no}', 'utf-8')
        
        # 连接并发送邮件
        with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
            server.login(sender, password)
            # 添加日志记录实际发送的收件人
            logger.info(f"正在发送邮件给: {receivers}")
            server.sendmail(sender, receivers, message.as_string())
            
        logger.info("发送邮件成功 - 工作档号: %s", job_file_no)
        
    except Exception as e:
        logger.error("发送邮件失败: %s", str(e))
        raise


if __name__ == "__main__":
    # 示例：使用通用函数更新BLOB字段
    inv_cn_id = 486406  # 示例发票ID
    remarks_text = "这是通过通用函数更新的内部备注"
    
    print("示例：使用通用函数更新BLOB字段...")
    print("=" * 50)
    
    # 使用通用函数
    success = update_blob_text_field_safe(
        table_name='INVOICE_COST_NOTE',
        field_name='INTERNAL_REMARKS',
        id_field='ID',
        record_id=inv_cn_id,
        text_content=remarks_text
    )
    
    if success:
        print("✅ 更新成功！")
        
        # 验证结果
        result = get_last_inv_cn_data(69632, 'cmsrpa01')
        if result:
            current_remarks = result.get('INTERNAL_REMARKS')
            print(f"当前内部备注: {current_remarks}")
    else:
        print("❌ 更新失败！")
    
    print("\n" + "=" * 50)
    print("可用的函数:")
    print("1. update_blob_text_field_safe() - 通用BLOB字段更新函数")
    print("2. update_internal_remarks() - 发票内部备注专用函数")
    print("3. get_last_inv_cn_data() - 获取最新发票记录函数")
    