# 提取指定job_date时间范围内的Postgres ob和Booking数据并导出为Excel文件，并给出下载链接（DWZ短链接）

import asyncio
import os
from datetime import datetime, timedelta
from typing import List, Dict
from utils.basic.logger_config import setup_logger
from utils.basic.optimized_export import export_both_formats
from utils.basic.pg_conn import get_postgres_connection_async, PG_DB_CMSDATA
from dotenv import load_dotenv

load_dotenv(override=True)

logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

async def fetch_job_data(begin_date: str, end_date: str, pro2_system_id: int) -> List[Dict]:
    """从Postgres中提取指定日期范围内的Job数据"""
    try:
        # Convert string dates to date objects
        begin_date_obj = datetime.strptime(begin_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
            query = """
            WITH job_latest AS (
              SELECT * FROM (
                SELECT j.*,
                       ROW_NUMBER() OVER (PARTITION BY j.job_id, j.pro2_system_id 
                                         ORDER BY j.id DESC) AS rn
                FROM t_job_details j
                WHERE j.job_date BETWEEN $1 AND $2 AND j.pro2_system_id = $3
              ) t WHERE rn = 1
            )
            SELECT * FROM job_latest
            """
            job_results = await connection.fetch(query, begin_date_obj, end_date_obj, pro2_system_id)
            job_data = [dict(record) for record in job_results]
            return job_data
    except Exception as e:
        logger.error(f"从Postgres提取数据失败: {e}")
        raise e
    
async def fetch_booking_data(begin_date: str, end_date: str, pro2_system_id: int) -> List[Dict]:
    """从Postgres中提取指定日期范围内的Booking数据"""
    try:
        # Convert string dates to date objects
        begin_date_obj = datetime.strptime(begin_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        async with get_postgres_connection_async(database=PG_DB_CMSDATA) as connection:
            query = """
            WITH booking_latest AS (
              SELECT * FROM (
                SELECT b.*,
                       ROW_NUMBER() OVER (PARTITION BY b.job_id, b.bkbl_id, b.pro2_system_id 
                                         ORDER BY b.id DESC) AS rn
                FROM t_booking_details b
                WHERE b.job_date BETWEEN $1 AND $2 AND b.pro2_system_id = $3
              ) t WHERE rn = 1
            )
            SELECT * FROM booking_latest
            """
            booking_results = await connection.fetch(query, begin_date_obj, end_date_obj, pro2_system_id)
            booking_data = [dict(record) for record in booking_results]
            return booking_data
    except Exception as e:
        logger.error(f"从Postgres提取数据失败: {e}")
        raise e
    
async def export_job_excel(begin_date: str, end_date: str, pro2_system_id: int = 86532) -> str:
    """导出Job数据为Excel文件"""
    job_data = await fetch_job_data(begin_date, end_date, pro2_system_id)
    filename_prefix = f"job_{begin_date}_{end_date}"
    download_url = await export_both_formats(job_data, filename_prefix, "job")
    return download_url

async def export_booking_excel(begin_date: str, end_date: str, pro2_system_id: int = 86532) -> str:
    """导出Booking数据为Excel文件"""
    booking_data = await fetch_booking_data(begin_date, end_date, pro2_system_id)
    filename_prefix = f"booking_{begin_date}_{end_date}"
    download_url = await export_both_formats(booking_data, filename_prefix, "booking")
    return download_url