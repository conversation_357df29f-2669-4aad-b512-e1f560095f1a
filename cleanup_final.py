#!/usr/bin/env python3
"""
PostgreSQL数据清理脚本（最终版本）
清理t_job_details和t_booking_details表中created_at > 2025-08-09的数据
"""

import sys
from utils.basic.pg_conn import get_postgres_connection
from utils.basic.logger_config import get_logger

logger = get_logger(__name__)

def cleanup_table_data(table_name: str, cutoff_date: str) -> int:
    """清理指定表中created_at大于指定日期的数据"""
    try:
        with get_postgres_connection() as conn:
            with conn.cursor() as cursor:
                # 首先检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    );
                """, (table_name,))
                result = cursor.fetchone()
                table_exists = result['exists'] if result else False
                
                if not table_exists:
                    logger.warning("表 %s 不存在，跳过", table_name)
                    return 0
                
                # 检查created_at字段是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = %s AND column_name = 'created_at'
                    );
                """, (table_name,))
                result = cursor.fetchone()
                has_created_at = result['exists'] if result else False
                
                if not has_created_at:
                    logger.warning("表 %s 没有created_at字段，跳过", table_name)
                    return 0

                # 统计要删除的记录数
                count_sql = f"SELECT COUNT(*) as count FROM {table_name} WHERE created_at > %s"
                cursor.execute(count_sql, (cutoff_date,))
                result = cursor.fetchone()
                record_count = result['count'] if result else 0

                if record_count == 0:
                    logger.info("表 %s 中没有需要删除的数据", table_name)
                    return 0

                logger.info("表 %s 中找到 %d 条需要删除的记录", table_name, record_count)

                # 执行删除操作
                delete_sql = f"DELETE FROM {table_name} WHERE created_at > %s"
                cursor.execute(delete_sql, (cutoff_date,))
                deleted_count = cursor.rowcount

                # 提交事务
                conn.commit()
                logger.info("成功从表 %s 删除了 %d 条记录", table_name, deleted_count)

                return deleted_count

    except Exception as e:
        logger.error("清理表 %s 时发生错误: %s", table_name, str(e))
        raise

def main():
    """主函数"""
    cutoff_date = "2025-08-09"
    tables_to_clean = ["t_job_details", "t_booking_details"]

    logger.info("开始清理数据，删除created_at > %s的记录", cutoff_date)

    total_deleted = 0

    for table_name in tables_to_clean:
        try:
            deleted_count = cleanup_table_data(table_name, cutoff_date)
            total_deleted += deleted_count
        except Exception as e:
            logger.error("清理表 %s 失败: %s", table_name, str(e))
            sys.exit(1)

    logger.info("数据清理完成，总共删除了 %d 条记录", total_deleted)

    # 显示清理后的统计信息
    try:
        with get_postgres_connection() as conn:
            with conn.cursor() as cursor:
                for table_name in tables_to_clean:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    result = cursor.fetchone()
                    remaining_count = result['count'] if result else 0
                    logger.info("表 %s 剩余记录数: %d", table_name, remaining_count)

    except Exception as e:
        logger.warning("获取清理后统计信息时发生错误: %s", str(e))

if __name__ == "__main__":
    # 安全确认
    print("⚠️  警告: 此脚本将删除PostgreSQL数据库中的数据!")
    print("将删除以下表中created_at > 2025-08-09的所有数据:")
    print("- t_job_details")
    print("- t_booking_details")
    print()

    confirm = input("确认要执行此操作吗？请输入 'YES' 来确认: ")
    if confirm != "YES":
        print("操作已取消")
        sys.exit(0)

    main()