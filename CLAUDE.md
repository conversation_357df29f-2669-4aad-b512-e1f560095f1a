# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A Python data processing application that integrates CMS business data from multiple Firebird databases across different locations (Shanghai, Qingdao, Hong Kong, Tokyo) into MySQL and PostgreSQL databases. The system supports sea and air freight profit analysis with advanced caching and batch processing capabilities.

## Development Commands

### Environment Setup
```bash
# Install dependencies using uv package manager
uv sync

# Run the main application
uv run python main.py

# Run tests
uv run python test_profit.py
```

### Database Operations
The system uses environment variables for multi-location database configurations. Ensure `.env` file is properly configured with:
- Firebird connections for multiple locations (QD, SH, HK, TY)
- MySQL target database configuration
- PostgreSQL configuration

## Architecture

### Database Connectivity Layer (`utils/basic/`)
- **fb_conn.py**: Firebird connection pooling with multi-charset support and location-based configurations
- **mysql_conn.py**: MySQL connection utilities with retry mechanisms
- **pg_conn.py**: PostgreSQL connection utilities with async support

### Business Logic Layer (`utils/database/`)
- **db_pro2_sea_air_profit.py**: Core profit analysis queries and business calculations
- **db_pro2_basic.py**: Batch processing orchestration with async support

### Utility Components (`utils/basic/`)
- **logger_config.py**: Centralized logging configuration with file and console output
- **data_cache_manager.py**: Global data caching system with cross-endpoint sharing

### Key Features
- **Multi-location database support**: Configurable Firebird connections for different geographical locations
- **Connection pooling**: Optimized Firebird connection pool with automatic cleanup
- **Batch processing**: Large date range queries split into smaller batches with concurrent execution
- **Intelligent caching**: Global cache manager with TTL and size limits
- **Retry mechanisms**: Robust error handling with exponential backoff
- **Character set flexibility**: Automatic charset detection for Firebird connections

### Location Configuration
The system uses the `LOCATION` environment variable to determine active database location:
- `QD`: Qingdao operations
- `SH`: Shanghai operations (default)
- `HK`: Hong Kong operations
- `TY`: Tokyo operations

### Profit Analysis Functions
- `get_booking_details_with_transhipment()`: Retrieves booking details with transhipment profit calculations
- `query_job_details_with_statistics_by_date()`: Job details with statistical analysis
- `calculate_profit_from_charges_unified()`: Unified profit calculation from various charge types

### Performance Optimizations
- Batch processing with configurable `BATCH_SIZE_DAYS` (default: 3 days)
- Concurrent batch execution with `MAX_CONCURRENT_BATCHES` (default: 5)
- Connection pooling with configurable min/max connections
- Global data caching with 30-minute default TTL

### Environment Variables
Key configurations in `.env`:
- `LOCATION`: Active location (QD/SH/HK/TY)
- `FB_HOST_*`: Firebird host configurations per location
- `MYSQL_*`: MySQL target database settings
- `PG_*`: PostgreSQL settings
- `DATA_CACHE_EXPIRE_MINUTES`: Cache TTL (default: 30)
- `DATA_CACHE_MAX_SIZE`: Max cache entries (default: 100)