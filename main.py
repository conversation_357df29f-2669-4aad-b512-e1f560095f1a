#!/usr/bin/env python3
"""
主程序入口
用于运行利润数据调度器
"""

import asyncio
from utils.app.profit_data_scheduler_pg import (
    setup_logger,
    ProfitDataScheduler
)

if __name__ == "__main__":
    logger = setup_logger(
        name="main",
        level="warning",
        log_to_console=True,
        log_to_file=True
    )
    
    scheduler = ProfitDataScheduler()
    asyncio.run(scheduler.start_scheduler())